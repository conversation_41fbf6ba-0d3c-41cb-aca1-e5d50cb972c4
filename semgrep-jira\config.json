{"gitlab_api_url": "https://git.egnyte-internal.com", "enable_ssdlc_check": true, "semgrep_api_base_url": "https://semgrep.dev/api/v1", "jira_url": "https://jira.egnyte-it.com", "semgrep_deployment_slug": "egnyte", "semgrep_deployment_id": 8907, "sast_docs": "https://egnyte.atlassian.net/wiki/spaces/PRSEC/pages/423166325/SAST+Standard+Operating+Procedure+SOP", "enabled_projects": ["CFS", "PINT", "DLAB", "MOB", "APPS", "EGD", "DEL", "DAR", "E2E", "SUP", "WS", "PRSEC", "EMT", "EHS"], "repository_filters": {"EHS": ["StorageSync/orchestrator", "StorageSync/platform", "StorageSync/synchronizer", "StorageSync/orchestrator", "StorageSync/platform", "StorageSync/audit_events", "smart-cache/pcc", "smart-cache/scache", "Turbo/turbo"]}, "ticket_description": "This ticket is automatically generated through the integration of Semgrep and Jira.\n\n*Please review the details carefully and take the necessary actions as indicated.*\n\n\\\\\nh2.Finding Description\n{}\n\nMore details and actions can be found *[in the Semgrep UI| {}]*\n\n\\\\\n\nh2.Links\n- *Issue in Semgrep*: {}  \n- *GitHub link*: {}\n\n\\\\\n\nh2.How to Handle SAST Issues\nSAST findings may sometimes present false positives. Here's what you can do:\n- If you believe this finding is a false positive, please update its status accordingly and close both the finding in Semgrep and this ticket.\n- If the finding is valid, please create a fix and close the ticket.\n- If you're unsure, feel free to reach out to the security team for assistance.\n\n*More information about the SAST process can be found [here| {}]*\n", "repository_mapping": {"internal_apps/egnyte-website-acquia": "WS", "integrations": "PINT", "desktop": "EGD", "webedit": "WEB", "UnifiedCMM": "EMT", "StorageSync": "EHS", "storagesync": "EHS", "internal_apps": "APPS", "ui": "CFS", "CFS-services": "CFS", "professional_services": "PS", "egnyte_services": "CFS", "data_warehouse": "DAR", "e2ee": "E2E", "delphi": "DEL", "mobile": "MOB", "ecl": "EMT", "support": "SUP", "ehs": "EHS", "competitive_intelligence": "VH", "smart-cache": "EHS", "connect": "CFS", "datalab": "DLAB", "devops": "IN", "Turbo": "EHS", "platform": "PSRV", "egnyte": "WS", "local_scan": "PRSEC", "unified.cmm": "EMT", "security-team": "PRSEC"}, "severity_mapping": {"global": {"critical": "Critical", "high": {"high": "Major", "medium": "Major", "low": "Minor", "default": "Major"}, "medium": {"high": "Minor", "medium": "pass", "low": "pass"}, "low": "pass"}, "per_project": {}}, "gcp_project_id": "infosec-prod-4d9a", "secret_ids": {"jira_token": "SEMGREP_JIRA_TOKEN", "semgrep_api_token": "SEMGREP_API_TOKEN", "slack_webhook": "SEMGREP_SLACK_WEBHOOK", "gitlab_token": "SEMGREP_GITLAB_TOKEN"}, "jira_ticket_assignees": {"WS": "bcollins"}, "jira_ticket_creator": "srv.security-jira", "semgrep_base_url": "https://semgrep.dev/orgs/egnyte/findings", "jql_finding_template": "summary ~ 'SAST Finding:'"}