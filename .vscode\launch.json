{"version": "0.2.0", "configurations": [{"name": "Debug Cloud Function", "type": "python", "request": "launch", "module": "functions_framework", "args": ["--target=main", "--port=5000", "--source=main.py", "--debug"], "cwd": "${workspaceFolder}/semgrep-jira/semgrep-jira", "env": {"PYTHONPATH": "${workspaceFolder}"}, "console": "integratedTerminal"}, {"name": "Debug Main.py Directly", "type": "python", "request": "launch", "program": "${workspaceFolder}/semgrep-jira/semgrep-jira/main.py", "cwd": "${workspaceFolder}/semgrep-jira/semgrep-jira", "console": "integratedTerminal"}]}