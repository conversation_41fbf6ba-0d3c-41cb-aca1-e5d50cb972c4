include:
  - project: gitlab/ci/oidc
    file: gcp_egnyte.yaml

default:
  image: google/cloud-sdk:slim
  before_script:
    - apt-get update && apt-get install -y zip

variables:
  WI_PROVIDER: //iam.googleapis.com/projects/************/locations/global/workloadIdentityPools/gitlab/providers/gitlab-provider
  SERVICE_ACCOUNT: ${GCP_SERVICE_ACCOUNT}
  TOKEN_LIFETIME: 1800
  GCLOUD_PROJECT: ${GCP_PROJECT}

stages:
  - deploy

deploy:
  extends:
    - .google-oidc:auth
  stage: deploy
  only:
    - master
  script:
    - cd semgrep-jira

    #- zip -r semgrep-jira-archive.zip .
    #- gsutil cp semgrep-jira-archive.zip gs://semgrep-jira-bucket/semgrep-jira-archive.zip
    - gcloud functions deploy semgrep-jira-2nd-gen --gen2 --region=us-central1 --runtime=python312 --entry-point=main --trigger-topic=semgrep-jira-topic --source="." --service-account=<EMAIL> --memory=512MB --timeout=360s --max-instances=1 --ingress-settings=internal-only
