import logging
import re
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Protocol
from dataclasses import dataclass
from prsec_integration_kit import JiraConnector, GCPSecretManager, ConfigLoader, GitLabConnector, SlackWebhook, SemgrepConnector
import functions_framework


# Protocol definitions for dependency injection
class ISemgrepClient(Protocol):
    def get_all_projects(self) -> List[Dict[str, Any]]: ...
    def get_all_findings(self) -> List[Dict[str, Any]]: ...

class IJiraClient(Protocol):
    def get_issues_by_jql(self, jql_query: str, max_results: int = 1000, fields: Optional[List[str]] = None) -> List[Any]: ...
    def create_issue_generic(self, fields: Dict[str, Any]): ...

class IGitlabClient(Protocol):
    def has_ssdlc_flag(self, repository_name: str) -> bool: ...

class ISlackNotifier(Protocol):
    def send_message(self, message: str) -> None: ...

@dataclass(frozen=True)
class Settings:
    repository_filters: Dict[str, List[str]]
    repository_mapping: Dict[str, str]
    severity_mapping: Dict[str, Any]
    ticket_description: str
    sast_docs: str
    enable_ssdlc_check: bool
    enabled_projects: List[str]
    jira_ticket_assignees: Dict[str, str]
    semgrep_base_url: str
    jql_finding_template: str


class TicketProcessor:
    """Handle processing of Semgrep findings and Jira ticket lifecycle."""

    def __init__(
        self,
        settings: Settings,
        semgrep_client: ISemgrepClient,
        jira_client: IJiraClient,
        slack_notifier: ISlackNotifier,
        gitlab_client: IGitlabClient,
        allowed_projects: Optional[List[str]] = None,
    ) -> None:
        """Initialize TicketProcessor with configuration and API clients."""
        self.settings = settings
        self.semgrep_client = semgrep_client
        self.jira_client = jira_client
        self.slack_notifier = slack_notifier
        self.gitlab_client = gitlab_client
        # If allowed_projects is None, allow all from settings
        self.allowed_projects = allowed_projects if allowed_projects is not None else settings.enabled_projects
        self.ssdlc_check_enabled = settings.enable_ssdlc_check
        # Pre-load existing tickets to avoid duplicate JQL calls
        self.existing_finding_ids = self._load_existing_ticket_ids()
    
    def _is_repo_allowed_for_project(self, project: str, repository: str) -> bool:
        """If a filter exists for the project, only allow matching repositories."""
        filters = self.settings.repository_filters.get(project)
        if not filters:
            return True
        return repository in filters


    def _load_existing_ticket_ids(self) -> set:
        """
        Load all finding IDs for which a Jira ticket already exists.
        Searches Jira for issues where the summary starts with "SAST Finding:" and
        extracts the numeric ID from the '(id: #<number>)' suffix.
        """
        # Fetch tickets created by this integration
        issues = self.jira_client.get_issues_by_jql(
            jql_query=self.settings.jql_finding_template,
            max_results=10000,
            fields=["summary"]
        )
        # Pattern matches '(id: #123456)' to capture the finding ID
        pattern = re.compile(r"\(id: #(\d+)\)")
        ids = set()
        for issue in issues:
            summary = getattr(issue.fields, "summary", "")
            match = pattern.search(summary)
            if match:
                ids.add(str(match.group(1)))
        logging.info("Loaded %d existing tickets", len(ids))
        return ids

    def _get_main_branch(self) -> Dict[str, str]:
        """Return a mapping of repository name to its primary branch name."""
        projects = self.semgrep_client.get_all_projects()
        return {
            proj["name"]: proj.get("primary_branch", "").split("/")[-1]
            for proj in projects
        }

    def _is_on_main_branch(self, finding: Dict[str, Any], main_branches: Dict[str, str]) -> bool:
        """Check if finding originates from the repository's main branch."""
        repo = finding.get("repository", {}).get("name", "")
        return main_branches.get(repo) == finding.get("ref")

    def _is_recent(self, timestamp: Optional[str], hours: int = 24) -> bool:
        """Return True if timestamp is within the last `hours` hours (UTC)."""
        if not timestamp:
            return False
        date = datetime.strptime(timestamp, "%Y-%m-%dT%H:%M:%S.%fZ").replace(tzinfo=timezone.utc)
        return (datetime.now(timezone.utc) - date) < timedelta(hours=hours)

    def _get_mapped_project(self, finding: Dict[str, Any]) -> Optional[str]:
        """Return Jira project key for the given finding, if any."""
        repo_name = finding.get("repository", {}).get("name", "")
        
        # Check if there's a direct mapping for the full repository name
        direct_mapping = self.settings.repository_mapping.get(repo_name)
        if direct_mapping:
            return direct_mapping
        
        # If no direct mapping exists, use standard group-based mapping
        group = repo_name.split("/")[0]
        return self.settings.repository_mapping.get(group)

    def _is_project_allowed(self, project: str) -> bool:
        """Check if project is in the allowed list (or if no restriction)."""
        return not self.allowed_projects or project in self.allowed_projects

    def process_findings(self) -> None:
        """Main entry point to process all Semgrep findings."""
        main_branches = self._get_main_branch()
        all_findings = self.semgrep_client.get_all_findings()

        self.slack_notifier.send_message(
            "🔍 Starting processing of Semgrep findings."
        )
        # Filter findings by main branch and recent timestamp
        filtered = [
            f for f in all_findings
            if self._is_on_main_branch(f, main_branches)
        ]
        logging.info("Processing %d findings after filtering", len(filtered))
        
        for finding in filtered:
            finding_id = finding.get("id")
            if (findin)
            project = self._get_mapped_project(finding)
            repo_name = finding.get("repository", {}).get("name", "")

            if project:
                if not self._is_project_allowed(project):
                    logging.info("Skipping finding not in allowed projects", extra={"finding_id": finding_id, "project": project})
                    continue
                
                if not self._is_repo_allowed_for_project(project, repo_name):
                    logging.info("Skipping: repo not permitted for project", extra={"finding_id": finding_id, "project": project, "repo": repo_name})
                    continue

                if self.ssdlc_check_enabled and not self.gitlab_client.has_ssdlc_flag(finding.get("repository", {}).get("name", "")):
                    logging.info("Skipping finding with no SSDLC flag", extra={"finding_id": finding_id})
                    continue
                if str(finding_id) in self.existing_finding_ids:
                    logging.info("Ticket already exists", extra={"finding_id": finding_id})
                    continue
                # Create Jira ticket
                self._create_ticket(finding, project)
            else:
                # Notify about unmapped finding
                self._notify_unmapped(finding)
        
        self.slack_notifier.send_message(
            "🏁 Finished processing"
        )

    def _create_ticket(self, finding: Dict[str, Any], project: str) -> None:
        """Create a Jira ticket for the given finding."""
        finding_id = finding.get("id")
        title = finding.get("rule_name", "No Title").replace("_", " ").title()
        finding_path = finding.get("line_of_code_url") or finding.get("location", {}).get("file_path", "")
        description = finding.get("rule_message", "")
        severity = finding.get("severity", "low").lower()
        confidence = finding.get("rule", {}).get("confidence", "").lower()

        # Map severity to Jira priority
        priority = self._map_severity(severity, confidence, project)
        if priority == "pass":
            return

        summary = f"SAST Finding: {title} in {finding_path.split('/')[-1]} (id: #{finding_id})"
        body = self.settings.ticket_description.format(
            description,
            f"{self.settings.semgrep_base_url}/{finding_id}",
            f"{self.settings.semgrep_base_url}/{finding_id}",
            finding_path,
            self.settings.sast_docs
        )

        ticket_fields = {
            "project": {"key": project},
            "summary": summary,
            "description": body,
            "issuetype": {"name": "Security Issue"},
            "priority": {"name": priority},
            "labels": ["SAST", "security_code_analysis"],
             "components": [{"name": "Security"}]
        }

        # Assign ticket if configured
        assignee = self.settings.jira_ticket_assignees.get(project)
        if assignee:
            ticket_fields["assignee"] = {"name": assignee}

        try:
            issue = self.jira_client.create_issue_generic(ticket_fields)
            self.slack_notifier.send_message(
                        f"✅ Created ticket {issue.key}: https://jira.egnyte-it.com/browse/{issue.key}"
                    )
            logging.info("Created ticket", extra={"ticket_key": issue.key})
        except Exception as e:
            
            self.slack_notifier.send_message(
                    f"❌ Failed to create ticket for finding {self.settings.semgrep_base_url}/{finding_id}"
                )
            logging.error(f"Failed to create ticket for {project} {e}")

    def _map_severity(self, severity: str, confidence: str, project: str) -> str:
        """Map Semgrep severity and confidence to Jira priority based on settings."""
        per_proj = self.settings.severity_mapping.get("per_project", {}).get(project, {})
        if severity in per_proj:
            return per_proj[severity]

        global_map = self.settings.severity_mapping.get("global", {})
        value = global_map.get(severity)
        if isinstance(value, dict):
            return value.get(confidence, value.get("default", severity.capitalize()))
        return value or severity.capitalize()

    def _notify_unmapped(self, finding: Dict[str, Any]) -> None:
        """Send Slack notification for findings without Jira mapping."""
        if not self._is_recent(finding.get("created_at")):
            return
        finding_id = finding.get("id")
        url = f"{self.settings.semgrep_base_url}/{finding_id}"
        message = f"New finding without valid mapping: {url}"
        self.slack_notifier.send_message(message)

@functions_framework.http
def main(request) -> Any:
    """HTTP entry point for Google Cloud Functions."""
    # Load request parameters
    
    params = request.args.to_dict() if request.method == "GET" else (request.get_json(silent=True) or {})
    allowed_projects = None
    if "projects" in params:
        raw = params["projects"]
        allowed_projects = raw if isinstance(raw, list) else [p.strip() for p in raw.split(",")]

    # Load configuration from file
    raw_conf = ConfigLoader(params.get("config", "config.json"))
    
    settings = Settings(
        severity_mapping=raw_conf["severity_mapping"],
        ticket_description=raw_conf["ticket_description"],
        sast_docs=raw_conf["sast_docs"],
        enable_ssdlc_check=raw_conf.get("enable_ssdlc_check", True),
        enabled_projects=raw_conf.get("enabled_projects", []),
        jira_ticket_assignees=raw_conf.get("jira_ticket_assignees", {}),
        semgrep_base_url=raw_conf.get('semgrep_base_url', ''),
        jql_finding_template=raw_conf.get('jql_finding_template', ''),
        repository_filters = raw_conf.get('repository_filters',{}),
        repository_mapping=raw_conf.get("repository_mapping",{})

    )

    # Retrieve secrets
    secret_mgr = GCPSecretManager(raw_conf.get("gcp_project_id"))
    secrets = {key: secret_mgr.get_secret(val) for key, val in raw_conf["secret_ids"].items()}

    # Initialize API clients
    semgrep_client = SemgrepConnector(secrets["semgrep_api_token"], slug_name=raw_conf.get("semgrep_deployment_slug"))
    jira_client = JiraConnector(secrets["jira_token"], raw_conf.get("jira_url"))
    slack_notifier = SlackWebhook("*******************************************************************************")
    gitlab_client = GitLabConnector(secrets["gitlab_token"], raw_conf.get("gitlab_api_url"))

    # Process findings
    processor = TicketProcessor(
        settings=settings,
        semgrep_client=semgrep_client,
        jira_client=jira_client,
        slack_notifier=slack_notifier,
        gitlab_client=gitlab_client,
        allowed_projects=allowed_projects
    )
    processor.process_findings()
    return ("Ticket creation process finished", 200)

if __name__ == "__main__":
    main(None)
