#!/usr/bin/env python3
import logging
import re
import json
import os
from datetime import datetime, timedelta, timezone

import requests
from jira import JIRA
from google.cloud import secretmanager
#import functions_framework

# Configure logging
logging.basicConfig(level=logging.INFO)

class ConfigLoader:
    """Responsible for loading and validating configuration from a JSON file."""
    
    @staticmethod
    def load_config(config_path="config.json") -> dict:
        """
        Loads configuration from a JSON file.
        
        Args:
            config_path (str): Path to the configuration file.
        
        Returns:
            dict: Configuration dictionary.
        
        Raises:
            FileNotFoundError: If the configuration file is not found.
            ValueError: If required configuration fields are missing.
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, "config.json")
        if not os.path.exists(config_path):
            logging.error("Configuration file %s not found.", config_path)
            raise FileNotFoundError(f"Configuration file {config_path} not found.")
        try:
            with open(config_path, 'r', encoding="utf-8") as f:
                config = json.load(f)
            logging.info("Configuration loaded from %s", config_path)
        except Exception as e:
            logging.error("Error loading configuration file: %s", e)
            raise

        required_fields = [
            "semgrep_api_base_url", "jira_url", "semgrep_deployment_slug",
            "semgrep_deployment_id",
           "gcp_project_id",
            "secret_ids", "closure_statuses"
        ]
        missing_fields = [field for field in required_fields if field not in config]
        if missing_fields:
            logging.error("Missing required configuration fields: %s", ", ".join(missing_fields))
            raise ValueError(f"Missing required configuration fields: {', '.join(missing_fields)}")
        return config

class SecretManager:
    """Handles retrieval of secrets from Google Cloud Secret Manager."""
    
    def __init__(self, config: dict):
        """
        Initializes the SecretManagerServiceClient with configuration.
        
        Args:
            config (dict): Configuration dictionary.
        """
        self.config = config
        self.client = secretmanager.SecretManagerServiceClient()
    
    def access_secret_version(self, secret_id: str, version_id: str = "latest") -> str:
        """
        Retrieves a secret value from Secret Manager.
        
        Args:
            secret_id (str): Identifier of the secret.
            version_id (str, optional): Secret version. Defaults to "latest".
        
        Returns:
            str: The secret value.
        """
        project_id = self.config.get("gcp_project_id", "")
        name = f"projects/{project_id}/secrets/{secret_id}/versions/{version_id}"
        response = self.client.access_secret_version(name=name)
        return response.payload.data.decode("UTF-8")
    
    def get_secrets(self) -> tuple:
        """
        Retrieves tokens for Jira and Semgrep API.
        
        Returns:
            tuple: (jira_token, semgrep_api_token)
        """
        secret_ids = self.config.get("secret_ids", {})
        jira_token = self.access_secret_version(secret_ids.get("jira_token", "SEMGREP_JIRA_TOKEN"))
        semgrep_api_token = self.access_secret_version(secret_ids.get("semgrep_api_token", "SEMGREP_API_TOKEN"))
        return jira_token, semgrep_api_token

class SemgrepClient:
    """Client for interacting with the Semgrep API."""
    
    def __init__(self, config: dict, semgrep_api_token: str):
        """
        Initializes the SemgrepClient.
        
        Args:
            config (dict): The configuration dictionary.
            semgrep_api_token (str): The Semgrep API token.
        """
        self.config = config
        self.api_token = semgrep_api_token
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
    def get_all_findings(self) -> list:
        """
        Fetches all findings from Semgrep based on configuration.
        
        Returns:
            list: List of finding dictionaries.
        """
        findings = []
        page = 0
        base_url = self.config["semgrep_api_base_url"]
        deployment_slug = self.config["semgrep_deployment_slug"]
        status_param = self.config.get("semgrep_findings_status", "ignored")
        issue_type = self.config.get("semgrep_issue_type", "sast")
        while True:
            url = f"{base_url}/deployments/{deployment_slug}/findings?page={page}&status={status_param}&issue_type={issue_type}"
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code != 200:
                logging.error("Error fetching findings: HTTP %s", response.status_code)
                break
            data = response.json().get("findings", [])
            if not data:
                break
            findings.extend(data)
            page += 1
        return findings
    
    def get_all_projects(self) -> list:
        """
        Fetches all projects from Semgrep.
        
        Returns:
            list: List of project dictionaries.
        """
        projects = []
        page = 0
        base_url = self.config["semgrep_api_base_url"]
        deployment_slug = self.config["semgrep_deployment_slug"]
        while True:
            url = f"{base_url}/deployments/{deployment_slug}/projects?page={page}"
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code != 200:
                logging.error("Error fetching projects: HTTP %s", response.status_code)
                break
            data = response.json().get("projects", [])
            if not data:
                break
            projects.extend(data)
            page += 1
        return projects
    
    @staticmethod
    def get_branch_name(ref: str) -> str:
        """
        Extracts the branch name from a git reference.
        
        Args:
            ref (str): The git reference.
        
        Returns:
            str: Branch name or None.
        """
        if ref:
            return ref.split("/")[-1]
        return None
    
    @staticmethod
    def parse_iso_datetime(dt_str: str) -> datetime:
        """
        Parses an ISO8601 datetime string.
        
        Args:
            dt_str (str): ISO8601 datetime string.
        
        Returns:
            datetime: Parsed datetime or None if error.
        """
        if dt_str.endswith("Z"):
            dt_str = dt_str[:-1] + "+00:00"
        try:
            return datetime.fromisoformat(dt_str)
        except Exception as e:
            logging.error("Error parsing datetime '%s': %s", dt_str, e)
            return None
    
    @staticmethod
    def is_updated_within_last_24h(updated_str: str) -> bool:
        """
        Checks if a datetime (ISO8601 string) is within the last 24 hours.
        
        Args:
            updated_str (str): Updated timestamp string.
        
        Returns:
            bool: True if within 24h, else False.
        """
        updated_dt = SemgrepClient.parse_iso_datetime(updated_str)
        if not updated_dt:
            return False
        threshold = datetime.now(timezone.utc) - timedelta(hours=24)
        return updated_dt > threshold
    
    @staticmethod
    def filter_findings_by_main_branch(findings: list, main_branches: dict) -> list:
        """
        Filters findings so that only those on the main branch are returned.
        
        Args:
            findings (list): List of finding dictionaries.
            main_branches (dict): Mapping of repository names to main branch names.
        
        Returns:
            list: Filtered findings.
        """
        filtered = []
        for finding in findings:
            repo_name = finding["repository"]["name"]
            if main_branches.get(repo_name) == finding["ref"]:
                logging.info("Processing finding ID: %s", finding["id"])
                filtered.append(finding)
        return filtered

class JiraClient:
    """Client for interacting with Jira."""
    
    def __init__(self, config: dict, jira_token: str):
        """
        Initializes the JiraClient.
        
        Args:
            config (dict): The configuration dictionary.
            jira_token (str): The Jira token.
        """
        self.config = config
        self.jira_token = jira_token
        self.session = JIRA(options={"server": self.config["jira_url"]}, token_auth=self.jira_token)
    
    def find_ticket_by_finding_id(self, finding_id: str):
        """
        Searches for a Jira ticket corresponding to a given finding id.
        
        Args:
            finding_id (str): The finding identifier.
        
        Returns:
            Issue or None: The found Jira ticket or None.
        """
        jql = self.config.get("jira_ticket_search_jql", f'summary ~ "(id: #{finding_id})" AND status NOT IN ("Closed", "Resolved", "DONE", "VERIFIED", "RELEASED", "APPROVED", "INTEGRATED")')
        try:
            jql_id = jql.format(str(finding_id))
            print(jql_id)
            issues = self.session.search_issues(jql_id, maxResults=0, validate_query=False)
            if issues:
                return issues[0]
        except Exception as e:
            logging.error("Error searching for Jira ticket for finding id %s: %s", finding_id, e)
        return None
    
    def close_ticket(self, ticket, triage_reason: str, finding_status: str):
        """
        Closes a Jira ticket by adding a comment and performing a transition.
        
        Args:
            ticket: The Jira ticket to close.
            triage_reason (str): Triage reason.
            finding_status (str): The status of the finding.
        """
        comment = (
            f"Closing ticket as the corresponding Semgrep finding was triaged with status '{finding_status}'.\n"
            f"Triage reason: {triage_reason}"
        )
        try:
            transitions = self.session.transitions(ticket)
            transition_id = None
            valid_transitions = [t.lower() for t in self.config.get("jira_closing_transitions", ["closed", "done", "resolved", "close without fixing"])]
            for t in transitions:
                if t["name"].lower() in valid_transitions:
                    transition_id = t["id"]
                    break
            
            # Custom handling for projects defined in custom_projects
            custom_projects = self.config.get("custom_projects", {})
            project_key = ticket.key.split('-')[0].upper()
            if project_key in custom_projects:
                cp_config = custom_projects[project_key]
                fields = cp_config.get("fields", {})
                transitions_seq = cp_config.get("transitions", [])
                if transitions_seq:
                    for i, trans in enumerate(transitions_seq):
                        self.session.transition_issue(ticket, trans, fields if i == 0 else None)
                    logging.info("Ticket %s closed using custom project config for project %s.", ticket.key, project_key)
                else:
                    logging.error("No transitions configured for custom project %s", project_key)
            elif transition_id:
                self.session.transition_issue(ticket, transition_id)
                logging.info("Ticket %s closed successfully.", ticket.key)
                self.session.add_comment(ticket, comment)
            else:
                logging.error("No valid transition found for ticket %s", ticket.key)
        except Exception as e:
            logging.error("Error closing ticket %s: %s", ticket.key, e)
    
    @staticmethod
    def extract_finding_id(summary: str) -> str:
        """
        Extracts the finding id from a Jira ticket summary.
        
        Args:
            summary (str): Ticket summary.
        
        Returns:
            str or None: Extracted finding id or None.
        """
        match = re.search(r"\(id:\s*#?([\w-]+)\)", summary)
        if match:
            return match.group(1)
        else:
            logging.warning("Could not extract finding id from summary: %s", summary)
            return None

class TicketProcessor:
    """Coordinates the processing of findings and corresponding Jira ticket operations."""
    
    def __init__(self, config: dict, semgrep_client: SemgrepClient, jira_client: JiraClient):
        """
        Initializes the TicketProcessor.
        
        Args:
            config (dict): Configuration dictionary.
            semgrep_client (SemgrepClient): Client for Semgrep API.
            jira_client (JiraClient): Client for Jira.
        """
        self.config = config
        self.semgrep_client = semgrep_client
        self.jira_client = jira_client
    
    def process(self):
        """
        Executes the processing pipeline:
          - Fetch findings and projects from Semgrep.
          - Determine main branches.
          - Filter findings to only those on main branches.
          - Process each finding that was triaged within the last 24 hours.
        """
        all_findings = self.semgrep_client.get_all_findings()
        logging.info("Fetched %d findings from Semgrep.", len(all_findings))
        
        projects = self.semgrep_client.get_all_projects()
        main_branches = {project["name"]: SemgrepClient.get_branch_name(project.get("primary_branch")) for project in projects}
        logging.info("Main branches mapping: %s", main_branches)
        
        filtered_findings = SemgrepClient.filter_findings_by_main_branch(all_findings, main_branches)
        logging.info("Filtered to %d findings on main branches.", len(filtered_findings))
        
        for finding in filtered_findings:
            updated_str = finding.get("triaged_at")
            if not updated_str or not SemgrepClient.is_updated_within_last_24h(updated_str):
                continue
            status = finding.get("status", "").lower()
            if status in self.config.get("closure_statuses", ["ignored", "fixed"]):
                finding_id = finding.get("id")
                if not finding_id:
                    logging.warning("Finding without id; skipping.")
                    continue
                triage_reason = finding.get("triage_comment", finding.get("rule_message", "No reason provided"))
                logging.info("Processing triaged finding id %s with status '%s'.", finding_id, status)
                ticket = self.jira_client.find_ticket_by_finding_id(finding_id)
                if ticket:
                    self.jira_client.close_ticket(ticket, triage_reason, status)
                else:
                    logging.warning("No matching Jira ticket found for finding id %s.", finding_id)
            else:
                logging.info("Finding id %s has status '%s' (not triaged); skipping.", finding.get("id"), status)
        logging.info("Triage processing complete.")

#@functions_framework.http
def main(request):
    """
    Main HTTP handler that loads configuration, retrieves secrets,
    and initiates the ticket processing.
    
    Args:
        request: The HTTP request object (for cloud functions).
    
    Returns:
        tuple: Response message and HTTP status code.
    """
    config_path = os.environ.get("CONFIG_PATH", "config.json")
    config = ConfigLoader.load_config(config_path)
    
    jira_token, semgrep_api_token = SecretManager(config).get_secrets()
    jira_client = JiraClient(config, jira_token)
    semgrep_client = SemgrepClient(config, semgrep_api_token)
    
    processor = TicketProcessor(config, semgrep_client, jira_client)
    processor.process()
    
    return "Triage processing complete.", 200

if __name__ == "__main__":
    # For local testing, pass a dummy request (in production, an HTTP request object will be used)
    main("test")
