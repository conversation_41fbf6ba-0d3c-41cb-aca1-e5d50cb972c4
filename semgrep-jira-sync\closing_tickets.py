import requests
import re
from jira import JIRA


semgrep_headers = {"Authorization": f"Bearer {SEMGREP_API_TOKEN}",
                   "Content-Type": "application/json"}

def connect_to_jira():
    """Nawiązuje połączenie z Jira przy użyciu biblioteki jira."""
    return JIRA(server=JIRA_BASE_URL, token_auth=JIRA_PAT)

def get_all_findings():
    """
    Pobiera wszystkie findings z Semgrep, iterując po stronach wyników.
    Używamy nowego endpointu API zgodnie z dokumentacją:
    https://semgrep.dev/api/v1/docs/#tag/Scan/operation/semgrep_app.foundations.scan.handlers.scan.openapi_list_scans
    Zakładamy, że odpowiedź zawiera pola: "findings" (lista wyników) oraz "has_more" (flaga, czy są kolejne strony).
    """
    findings = []
    semgrep_url = "https://semgrep.dev/api/v1/deployments/egnyte/findings"
    page = 0
    while True:
        paged_url = f"{semgrep_url}?page={page}&status=open"
        response = requests.get(paged_url, headers=semgrep_headers, timeout=10)
        data = response.json().get("findings", [])
        if not data:
            break
        findings.extend(data)
        page += 1
    return findings


def get_low_confidence_findings():
    """
    Filtruje findings o średniej istotności i niskiej pewności.
    Najpierw pobiera wszystkie findings, a następnie wybiera te, które spełniają warunki.
    """
    all_findings = get_all_findings()
    low_confidence_findings = []

    for finding in all_findings:
        severity = finding.get("severity", "").lower()
        confidence = finding.get("confidence", "").lower()

        if severity == "medium" and (confidence == "low" or confidence == "medium"):
            finding_id = finding.get("id")
            if finding_id:
                low_confidence_findings.append(str(finding_id))

    print(f"Znaleziono {len(low_confidence_findings)} findings o średniej istotności i niskiej pewności.")
    return low_confidence_findings

def find_matching_jira_tickets(jira, finding_ids):
    """
    Znajduje tickety w Jira utworzone przez 'srv.security-jira', 
    których pole 'summary' zawiera identyfikator findingu w formacie:
    "SAST Finding: ... (id: #<finding_id>)".
    Przetwarza ID findingów w partiach, aby uniknąć ograniczeń długości zapytań JQL.
    """
    matching_tickets = []
    batch_size = 20

    for i in range(0, len(finding_ids), batch_size):
        batch = finding_ids[i:i+batch_size]
        # Dla każdego ID tworzymy warunek wyszukiwania:
        # summary ~ '"(id: #<finding_id>)"'
        id_conditions = [f"summary ~ '\"(id: #{fid})\"'" for fid in batch]
        id_query = " OR ".join(id_conditions)
        jql_query = f'reporter = "srv.security-jira" AND ({id_query})'
        print(f"Wyszukiwanie ticketów z JQL: {jql_query}")

        issues = jira.search_issues(jql_query, maxResults=100)
        print(f"Znaleziono {len(issues)} ticketów w tej partii.")
        matching_tickets.extend(issues)

    print(f"Łącznie znaleziono {len(matching_tickets)} ticketów Jira odpowiadających podanym findingom.")
    return matching_tickets

def close_jira_ticket(jira, issue):
    """
    Zamyka dany ticket w Jira poprzez dodanie komentarza i wykonanie przejścia 'Close'.
    """
    # Dodajemy komentarz
    
    # Szukamy odpowiedniej akcji przejścia do zamknięcia ticketu
    transitions = jira.transitions(issue)
    close_transition_id = None

    for transition in transitions:
        if transition['name'].lower() in ["close", "closed", "close issue", "close without fixing"]:
            close_transition_id = transition['id']
            break

    if not close_transition_id:
        print(f"Brak dostępnej opcji zamknięcia dla ticketu {issue.key}")
        return False

    jira.transition_issue(issue, close_transition_id, fields={"resolution": {"name": "Won't Fix"}})
    print(f"Pomyślnie zamknięto ticket {issue.key}")
    jira.add_comment(issue.key, "delete due to mapping adjustment")
    return True

def extract_finding_id(summary):
    """Wydobywa ID findingu z opisu ticketu przy użyciu wyrażenia regularnego."""
    match = re.search(r'id: #(\d+)', summary)
    if match:
        return match.group(1)
    return None

def main():
    # Pobierz findings o średniej istotności i niskiej pewności
    low_confidence_finding_ids = get_low_confidence_findings()
    if not low_confidence_finding_ids:
        print("Brak findings spełniających kryteria. Kończę działanie.")
        return

    # Połącz się z Jira
    jira = connect_to_jira()

    # Wyszukaj tickety Jira odpowiadające znalezionym findingom
    matching_tickets = find_matching_jira_tickets(jira, low_confidence_finding_ids)
    if not matching_tickets:
        print("Brak pasujących ticketów Jira. Kończę działanie.")
        return

    # Przetwarzaj znalezione tickety i zamykaj je
    for issue in matching_tickets:
        summary = issue.fields.summary
        finding_id = extract_finding_id(summary)

        print(f"Przetwarzanie ticketu {issue.key} dla finding ID: {finding_id}")
        print(f"Opis ticketu: {summary}")

        close_jira_ticket(jira, issue)

if __name__ == "__main__":
    main()