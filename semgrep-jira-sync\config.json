{"semgrep_api_base_url": "https://semgrep.dev/api/v1", "jira_url": "https://jira-staging.egnyte-it.com", "semgrep_deployment_slug": "egnyte", "semgrep_deployment_id": 8907, "gcp_project_id": "infosec-prod-4d9a", "secret_ids": {"jira_token": "SEMGREP_JIRA_TOKEN", "semgrep_api_token": "SEMGREP_API_TOKEN", "slack_webhook": "SEMGREP_SLACK_WEBHOOK"}, "closure_statuses": ["ignored", "fixed"], "jira_ticket_search_jql": "creator=srv.security-jira AND summary ~ \"(id: #{})\" AND status NOT IN (\"Closed\", \"Resolved\", \"DONE\", \"VERIFIED\", \"RELEASED\", \"APPROVED\", \"INTEGRATED\")", "jira_ticket_creator": "srv.security-jira", "jira_closing_transitions": ["closed", "done", "resolved", "close without fixing"], "custom_projects": {"DLAB": {"fields": {"customfield_13000": "semgrep", "customfield_10004": 0}, "transitions": [11, 131, 81]}, "OTHER": {"fields": {"customfield_20000": "other_value"}, "transitions": [21, 22]}}, "semgrep_findings_status": "ignored", "semgrep_issue_type": "sast"}